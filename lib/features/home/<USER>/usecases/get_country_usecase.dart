import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/country_response.dart';
import '../../data/repositories/home_repository.dart';

class GetCountryUseCase {
  final HomeRepository repository;

  GetCountryUseCase(this.repository);

  Future<Result<CountryResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    return await repository.getCountry(
      token: token,
      userId: userId,
      isSync: isSync,
    );
  }
}