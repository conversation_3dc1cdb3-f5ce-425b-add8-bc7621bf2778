import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/state_response.dart';
import '../../data/repositories/home_repository.dart';

class GetStateUseCase {
  final HomeRepository repository;

  GetStateUseCase(this.repository);

  Future<Result<StateResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    return await repository.getState(
      token: token,
      userId: userId,
      isSync: isSync,
    );
  }
}