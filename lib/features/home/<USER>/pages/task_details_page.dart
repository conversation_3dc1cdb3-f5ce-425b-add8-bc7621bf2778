import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:get_it/get_it.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_state.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/store_info_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/task_completion_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/pos_received_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/form_queue_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/overview_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/alert_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/expandable_task_sections_view.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/complete_task_view.dart';
import 'package:storetrack_app/shared/widgets/timer_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/cubits/keyboard_visibility_cubit.dart';
import 'package:storetrack_app/shared/cubits/keyboard_visibility_state.dart';

enum TaskViewMode {
  overview,
  alert,
  expandableSections,
  completeTask,
}

@RoutePage()
class TaskDetailsPage extends StatelessWidget {
  final int taskId;
  final num storeId;

  /// If true, the page will open with the Alert section expanded and selected.
  /// Defaults to false.
  final bool openBrief;

  /// If true, the page will open with the expandable sections (Brief and Documents) shown.
  /// Defaults to false.
  /// Note: If both [openBrief] and [openDocuments] are set to true, [openDocuments]
  /// takes precedence and the expandable sections will be shown.
  final bool openDocuments;

  const TaskDetailsPage({
    super.key,
    required this.taskId,
    this.openBrief = false,
    this.openDocuments = false,
    required this.storeId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<TaskDetailsCubit>(),
      child: _TaskDetailsPageContent(
        taskId: taskId,
        storeId: storeId,
        openBrief: openBrief,
        openDocuments: openDocuments,
      ),
    );
  }
}

class _TaskDetailsPageContent extends StatefulWidget {
  final int taskId;
  final num storeId;
  final bool openBrief;
  final bool openDocuments;

  const _TaskDetailsPageContent({
    required this.taskId,
    required this.storeId,
    this.openBrief = false,
    this.openDocuments = false,
  });

  @override
  State<_TaskDetailsPageContent> createState() =>
      _TaskDetailsPageContentState();
}

class _TaskDetailsPageContentState extends State<_TaskDetailsPageContent> {
  FormProgress? formProgress;
  TaskViewMode _currentViewMode = TaskViewMode.overview;
  bool _isTimerVisible = false;
  bool _allMandatoryFormsCompleted = false;

  // Key to access CompleteTaskView for submission
  final GlobalKey<CompleteTaskViewState> _completeTaskKey =
      GlobalKey<CompleteTaskViewState>();

  @override
  void initState() {
    super.initState();
    // Initialize the default view based on the incoming flags.
    // If both are true, expandable sections takes precedence.
    if (widget.openDocuments) {
      _currentViewMode = TaskViewMode.expandableSections;
    } else if (widget.openBrief) {
      _currentViewMode = TaskViewMode.alert;
    } else {
      _currentViewMode = TaskViewMode.overview;
    }
    // Fetch task details when the page initializes
    context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
    // Check mandatory forms completion status
    _checkAllMandatoryFormsCompleted();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh task data and form progress when returning from other pages (e.g., PosPage)
    try {
      context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
      _calculateFormProgress();
      _checkAllMandatoryFormsCompleted();
    } catch (e) {
      // Handle any errors in dependency refresh gracefully
    }
  }

  Future<void> _calculateFormProgress() async {
    try {
      final progress = await FormUtils.getFormPageProgress(
        taskId: widget.taskId,
      );
      if (mounted) {
        setState(() {
          formProgress = progress;
        });
      }
    } catch (e) {
      // Handle form progress calculation error gracefully
      if (mounted) {
        setState(() {
          formProgress = null;
        });
      }
      // Error is handled gracefully - form progress is optional
    }
  }

  Future<void> _checkAllMandatoryFormsCompleted() async {
    try {
      final mandatoryFormsProgress = await FormUtils.getMandatoryFormsProgress(
        taskId: widget.taskId,
      );

      logger(
          'Mandatory forms progress: ${mandatoryFormsProgress.totalVisible} ${mandatoryFormsProgress.totalCompleted}');

      final allFormsCompleted = mandatoryFormsProgress.totalVisible ==
          mandatoryFormsProgress.totalCompleted;

      if (mounted) {
        setState(() {
          _allMandatoryFormsCompleted = allFormsCompleted;
        });
      }
    } catch (e) {
      // Handle error gracefully - default to false for safety
      if (mounted) {
        setState(() {
          _allMandatoryFormsCompleted = false;
        });
      }
    }
  }

  PopupMenuButton<String> _buildPopupMenu(entities.TaskDetail? task) {
    return PopupMenuButton<String>(
      icon: Image.asset(
        AppAssets.taskMore,
        scale: 4,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 8,
      offset: const Offset(0, 20),
      color: Colors.white,
      position: PopupMenuPosition.under,
      constraints: const BoxConstraints(
        minWidth: 240,
        maxWidth: 320,
      ),
      itemBuilder: (context) => [
        _buildPopupMenuItem('Forms', AppAssets.taskForm),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem('POS', AppAssets.posIcon),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem('Note', AppAssets.alertMessage),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem('Directions', AppAssets.appbarMap),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem('Store info', AppAssets.taskStore),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem('Store history', AppAssets.taskStoryHistory),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem('Task assistance', AppAssets.taskAssistant),
        if (true) ...[
          const PopupMenuDivider(height: .2),
          _buildPopupMenuItem(
            'Complete task',
            AppAssets.taskComplete,
            isBlue: true,
          ),
        ],
      ],
      onSelected: (value) =>
          task != null ? _handleMenuSelection(value, task) : null,
    );
  }

  PopupMenuItem<String> _buildPopupMenuItem(String title, String icon,
      {bool isBlue = false}) {
    return PopupMenuItem<String>(
      value: title,
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                  color: isBlue ? AppColors.primaryBlue : Colors.black,
                ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Image.asset(
                icon,
                scale: 3,
                color: Colors.black,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value, entities.TaskDetail task) {
    switch (value) {
      case 'Forms':
        context.navigateTo(FormRoute(
          taskId: (task.taskId ?? 0).toInt(),
        ));
        break;
      case 'POS':
        context.navigateTo(PosRoute(
          task: task,
        ));
        break;
      case 'Note':
        context.navigateTo(NotesRoute(
          task: task,
        ));
        break;
      case 'Directions':
        _openGoogleMaps();
        break;
      case 'Store info':
        context.navigateTo(StoreInfoRoute(
            storeId: widget.storeId.toString(),
            taskId: widget.taskId.toString()));
        break;
      case 'Store history':
        context.navigateTo(StoreHistoryRoute(
          storeId: (task.storeId ?? 0).toInt(),
          taskId: (task.taskId ?? 0).toInt(),
        ));
        break;
      case 'Task assistance':
        break;
      case 'Complete task':
        if (!_allMandatoryFormsCompleted) {
          SnackBarService.warning(
            context: context,
            message:
                'You have not completed all mandatory forms. Please complete all mandatory forms before completing the task.',
          );
        }
        setState(() {
          _currentViewMode = _currentViewMode == TaskViewMode.completeTask
              ? TaskViewMode.overview
              : TaskViewMode.completeTask;
        });
        break;
    }
  }

  void _toggleMainView(String view) {
    setState(() {
      if (view == 'alert') {
        _currentViewMode = _currentViewMode == TaskViewMode.alert
            ? TaskViewMode.overview
            : TaskViewMode.alert;
      } else if (view == 'documents') {
        _currentViewMode = _currentViewMode == TaskViewMode.expandableSections
            ? TaskViewMode.overview
            : TaskViewMode.expandableSections;
      }
    });
  }

  void _toggleTimer() {
    setState(() {
      _isTimerVisible = !_isTimerVisible;
    });
  }

  Future<void> _openGoogleMaps() async {
    final state = context.read<TaskDetailsCubit>().state;
    if (state is! TaskDetailsSuccess) {
      if (mounted) {
        SnackBarService.warning(
          context: context,
          message: 'Task details not loaded. Please wait and try again.',
        );
      }
      return;
    }

    final task = state.taskDetail;
    try {
      double? latitude;
      double? longitude;

      // Try task-specific coordinates first
      if (task.taskLatitude != null && task.taskLongitude != null) {
        try {
          latitude = task.taskLatitude!.toDouble();
          longitude = task.taskLongitude!.toDouble();
        } catch (e) {
          // Handle conversion error gracefully
        }
      }

      // Fallback to general coordinates
      if ((latitude == null || longitude == null) &&
          task.latitude != null &&
          task.longitude != null) {
        try {
          latitude = task.latitude!.toDouble();
          longitude = task.longitude!.toDouble();
        } catch (e) {
          // Handle conversion error gracefully
        }
      }

      if (latitude == null ||
          longitude == null ||
          latitude == 0.0 ||
          longitude == 0.0) {
        if (mounted) {
          SnackBarService.warning(
            context: context,
            message: 'Location coordinates not available for this task.',
          );
        }
        return;
      }

      final googleMapsUrl =
          'https://www.google.com/maps?q=$latitude,$longitude';

      // Navigate to Google Maps via web browser
      context.router
          .push(WebBrowserRoute(url: googleMapsUrl, title: 'Google Maps'));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Unable to open map. Please try again later.',
        );
      }
      // Error handled with user-friendly message
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FormRefreshCubit, FormRefreshState>(
      listener: (context, refreshState) {
        if (refreshState is RefreshForm) {
          // Recalculate form progress when refresh is triggered
          _calculateFormProgress();
          // Also refresh the task details data to show latest information
          context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
          // Check mandatory forms completion status to update Complete task button visibility
          _checkAllMandatoryFormsCompleted();
        }
      },
      child: BlocBuilder<TaskDetailsCubit, TaskDetailsState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: (_currentViewMode == TaskViewMode.alert)
                ? Colors.white
                : AppColors.lightGrey2,
            appBar: CustomAppBar(
              title: 'Task Details',
              actions: [
                GestureDetector(
                  onTap: () => _toggleMainView('alert'),
                  child: Image.asset(
                    AppAssets.alertIcon,
                    scale: 4,
                    color: _currentViewMode == TaskViewMode.alert
                        ? AppColors.primaryBlue
                        : AppColors.black,
                  ),
                ),
                const Gap(12),
                GestureDetector(
                  onTap: _toggleTimer,
                  child: Icon(
                    Icons.timer_outlined,
                    color: _isTimerVisible
                        ? AppColors.primaryBlue
                        : AppColors.black,
                  ),
                ),
                const Gap(16),
                GestureDetector(
                  onTap: () => _toggleMainView('documents'),
                  child: Image.asset(
                    AppAssets.documentsIcon,
                    color: _currentViewMode == TaskViewMode.expandableSections
                        ? AppColors.primaryBlue
                        : AppColors.black,
                    scale: 4,
                  ),
                ),
                const Gap(4),
                _buildPopupMenu(
                    state is TaskDetailsSuccess ? state.taskDetail : null),
              ],
            ),
            body: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              behavior: HitTestBehavior.opaque,
              child: _buildBody(context, state),
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            floatingActionButton:
                BlocBuilder<KeyboardVisibilityCubit, KeyboardVisibilityState>(
              builder: (context, keyboardState) {
                final isKeyboardVisible = keyboardState is KeyboardVisible;
                if (!_allMandatoryFormsCompleted || isKeyboardVisible) {
                  return const SizedBox.shrink();
                }
                return Container(
                  height: 56,
                  decoration: BoxDecoration(
                    color: AppColors.midGrey,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_currentViewMode == TaskViewMode.completeTask) ...[
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _currentViewMode = TaskViewMode.overview;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Icon(
                              Icons.close_rounded,
                              color: AppColors.black,
                            ),
                          ),
                        ),
                        const Gap(8),
                      ],
                      SizedBox(
                        width: 128,
                        child: AppButton(
                          text: _currentViewMode == TaskViewMode.completeTask
                              ? "Submit"
                              : "Complete task",
                          color: AppColors.primaryBlue,
                          textColor: Colors.white,
                          onPressed: () async {
                            if (_currentViewMode == TaskViewMode.completeTask) {
                              // Call the CompleteTaskView's submitTask method
                              final state = _completeTaskKey.currentState;
                              if (state != null) {
                                await state.submitTask();
                              }
                            } else {
                              // Toggle to complete task view
                              setState(() {
                                _currentViewMode = TaskViewMode.completeTask;
                              });
                            }
                          },
                          height: 40,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, TaskDetailsState state) {
    if (state is TaskDetailsLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is TaskDetailsError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const Gap(16),
            Text(
              'Error loading task details',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Text(
              state.message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const Gap(16),
            ElevatedButton(
              onPressed: () {
                context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    } else if (state is TaskDetailsSuccess) {
      final task = state.taskDetail;

      return Column(
        children: [
          if (_isTimerVisible)
            TimerBar(
              taskId: task.taskId?.toInt(),
              budget: Duration(minutes: task.budget?.toInt() ?? 0),
            ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  StoreInfoView(task: task),
                  _currentViewMode == TaskViewMode.alert
                      ? Container(
                          color: Colors.white,
                          child: const Divider(),
                        )
                      : const Gap(16),
                  _buildPageContent(context, task),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return const Center(
        child: EmptyState(message: 'No task data available'),
      );
    }
  }

  Widget _buildPageContent(BuildContext context, entities.TaskDetail task) {
    switch (_currentViewMode) {
      case TaskViewMode.alert:
        return AlertView(task: task);
      case TaskViewMode.expandableSections:
        return ExpandableTaskSectionsView(task: task);
      case TaskViewMode.completeTask:
        return CompleteTaskView(
          key: _completeTaskKey,
          task: task,
          onSubmit: () {
            // Hide the complete task view after successful submission
            setState(() {
              _currentViewMode = TaskViewMode.overview;
            });
            // Navigate back to previous page after successful sync
            context.router.maybePop();
          },
        );
      case TaskViewMode.overview:
        return _buildDefaultView(context, task);
    }
  }

  Widget _buildDefaultView(BuildContext context, entities.TaskDetail task) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: TaskCompletionView(
                  task: task,
                  formProgress: formProgress,
                ),
              ),
              const Gap(16),
              Expanded(child: PosReceivedView(task: task)),
            ],
          ),
        ),
        const Gap(16),
        const Divider(color: AppColors.midGrey),
        const Gap(8),
        FormQueueView(task: task),
        const Gap(16),
        const Divider(color: AppColors.midGrey),
        OverviewView(
          task: task,
          onDocumentsTap: () => _toggleMainView('documents'),
        ),
        const Gap(16),
      ],
    );
  }
}
