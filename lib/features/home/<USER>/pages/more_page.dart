import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/file_cleanup_utils.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/shared/cubits/connectivity_cubit.dart';

@RoutePage()
class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  State<MorePage> createState() => _MorePageState();
}

class _MorePageState extends State<MorePage>
    with SingleTickerProviderStateMixin {
  bool notificationEnabled = true;
  bool tutorialEnabled = true;
  bool adminAccess = false;
  String version = "Loading..."; // Will be updated with actual version
  String lastSyncTime = "Not synced yet";
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
    _loadSettings();
    _loadLastSyncTime();
    _loadAdminAccess();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  Future<void> _loadAppVersion() async {
    try {
      final appInfoService = sl<AppInfoService>();
      final appVersion = await appInfoService.getVersion();
      setState(() {
        version = appVersion;
      });
    } catch (e) {
      setState(() {
        version = "Unknown";
      });
    }
  }

  Future<void> _loadSettings() async {
    try {
      final dataManager = sl<DataManager>();
      final notificationSetting = await dataManager.getNotificationEnabled();
      final tutorialSetting = await dataManager.getTutorialEnabled();

      setState(() {
        notificationEnabled = notificationSetting;
        tutorialEnabled = tutorialSetting;
      });
    } catch (e) {
      // Handle error loading settings, keep defaults
    }
  }

  Future<void> _loadLastSyncTime() async {
    try {
      final dataManager = sl<DataManager>();
      final syncTime = await dataManager.getLastSyncTime();

      setState(() {
        if (syncTime != null) {
          final formatter = DateFormat('MMM dd, yyyy h:mm a');
          lastSyncTime = 'Last synced: ${formatter.format(syncTime)}';
        } else {
          lastSyncTime = 'Not synced yet';
        }
      });
    } catch (e) {
      // Handle error loading sync time, keep default
      setState(() {
        lastSyncTime = 'Not synced yet';
      });
    }
  }

  Future<void> _loadAdminAccess() async {
    try {
      final dataManager = sl<DataManager>();
      final adminAccessStatus = await dataManager.getAdminAccess();

      setState(() {
        adminAccess = adminAccessStatus;
      });
    } catch (e) {
      // Handle error loading admin access, keep default
      setState(() {
        adminAccess = false;
      });
    }
  }

  Future<void> _saveNotificationSetting(bool value) async {
    try {
      final dataManager = sl<DataManager>();
      await dataManager.saveNotificationEnabled(value);
      setState(() {
        notificationEnabled = value;
      });
    } catch (e) {
      // Handle error saving setting
    }
  }

  Future<void> _saveTutorialSetting(bool value) async {
    try {
      final dataManager = sl<DataManager>();
      await dataManager.saveTutorialEnabled(value);
      setState(() {
        tutorialEnabled = value;
      });
    } catch (e) {
      // Handle error saving setting
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'More',
        showBackButton: false,
        onBackPressed: () {
          // tabsRouter.setActiveIndex(0);
        },
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSettingItem(
                title: 'Notifications',
                // icon: Icons.notifications_outlined,
                trailing: Switch(
                  value: notificationEnabled,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  onChanged: _saveNotificationSetting,
                  activeColor: AppColors.primaryBlue,
                ),
              ),
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'Tutorial',
                // icon: Icons.school_outlined,
                trailing: Switch(
                  value: tutorialEnabled,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  onChanged: _saveTutorialSetting,
                  activeColor: AppColors.primaryBlue,
                ),
              ),
              // const Divider(height: 1, color: AppColors.lightGrey2),
              // _buildSettingItem(
              //   title: 'Tutorial Guide',
              //   // icon: Icons.school_outlined,
              //   onTap: () {
              //     TutorialDialog.show(context);
              //   },
              // ),
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'Reset Data',
                // icon: Icons.restore_outlined,
                onTap: () async {
                  await ConfirmDialog.show(
                    context: context,
                    title: 'Delete StoreTrack Data',
                    message:
                        'Danger zone. Please use this page only when you have trouble with the app. All unsynced data will be erased.',
                    confirmText: 'Delete',
                    cancelText: 'Cancel',
                    onConfirm: () async {
                      final realmDatabase = sl<RealmDatabase>();
                      realmDatabase.clearTaskDetailData();
                      await FileCleanupUtils.clearLocalFiles();
                    },
                  );
                },
              ),
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'View Terms & Conditions',
                // icon: Icons.description_outlined,
                onTap: () {
                  context.router.push(WebBrowserRoute(
                    url:
                        'https://webservice2.storetrack.com.au/doc/terms/terms.pdf',
                    title: 'Terms & Conditions',
                  ));
                },
              ),
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'View Privacy Policy',
                // icon: Icons.privacy_tip_outlined,
                onTap: () {
                  context.router.push(WebBrowserRoute(
                    url: 'https://www.crossmark.com.au/privacy/',
                    title: 'Privacy Policy',
                  ));
                },
              ),
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'About Store Track',
                // icon: Icons.info_outline,
                onTap: () {
                  context.router.push(WebBrowserRoute(
                    url:
                        'https://webservice2.storetrack.com.au/doc/about/aboutstoretrack.html',
                    title: 'About Store Track',
                  ));
                },
              ),
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'Useful Links',
                // icon: Icons.link_outlined,
                onTap: () {
                  context.router.push(const UsefulLinksRoute());
                },
              ),
              if (adminAccess) ...[
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'Preview Form',
                  // icon: Icons.preview_outlined,
                  onTap: () {
                    // Navigate directly to Dashboard with FormRoute to avoid router context issues
                    context.router.replaceAll([
                      HomeRoute(children: [
                        DashboardHolderRoute(children: [
                          FormRoute(taskId: 0),
                        ])
                      ])
                    ]);
                  },
                ),
              ],
              const Divider(height: 1, color: AppColors.lightGrey2),
              _buildSettingItem(
                title: 'Version',
                version: version,
                isVersion: true,
                // icon: Icons.link_outlined,
                onTap: () {
                  // Show useful links
                },
              ),
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    _buildActionButton(
                      title: 'Sync Data',
                      // icon: Icons.sync_outlined,
                      onPressed: () async {
                        await SyncService().sync(context: context);
                        // Reload sync time after sync completes
                        _loadLastSyncTime();
                      },
                      backgroundColor: AppColors.loginGreen,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.access_time,
                          size: 14,
                          color: AppColors.blackTint1,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          lastSyncTime,
                          style: textTheme.montserratParagraphXsmall.copyWith(
                              // color: AppColors.blackTint1,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    BlocBuilder<ConnectivityCubit, ConnectivityState>(
                      bloc: sl<ConnectivityCubit>(),
                      builder: (context, state) {
                        final connectivityCubit = sl<ConnectivityCubit>();
                        final workOfflineEnabled =
                            connectivityCubit.isWorkOfflineEnabled;

                        return _buildActionButton(
                          title: workOfflineEnabled
                              ? 'Work Online'
                              : 'Work Offline',
                          onPressed: () async {
                            await connectivityCubit
                                .setWorkOfflineMode(!workOfflineEnabled);
                          },
                          backgroundColor: workOfflineEnabled
                              ? AppColors.darkOrange50
                              : AppColors.primaryBlue,
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required String title,
    // required IconData icon,
    Widget? trailing,
    String? version,
    VoidCallback? onTap,
    bool isVersion = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          child: Row(
            children: [
              // Icon(
              //   icon,
              //   size: 22,
              //   color: AppColors.primaryBlue,
              // ),
              // const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const Spacer(),
              isVersion
                  ? Text(
                      version ?? '',
                      style: Theme.of(context)
                          .textTheme
                          .montserratParagraphSmall
                          .copyWith(
                            color: AppColors.blackTint1,
                          ),
                    )
                  : const SizedBox(),

              isVersion
                  ? const SizedBox()
                  : trailing ??
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: AppColors.blackTint1,
                      ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    // required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: backgroundColor.withValues(alpha: 0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 14),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style:
                  Theme.of(context).textTheme.montserratParagraphSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
