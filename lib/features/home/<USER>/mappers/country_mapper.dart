import '../models/country_response.dart';
import '../models/country_model.dart' as country_realm;

class CountryMapper {
  static country_realm.CountryModel toModel(CountryResponse response) {
    return country_realm.CountryModel(
      "country",
      response.toRawJson(),
      DateTime.now(),
    );
  }

  static CountryResponse toEntity(country_realm.CountryModel model) {
    return CountryResponse.fromRawJson(model.jsonData);
  }

  static void updateModel({
    required country_realm.CountryModel existingModel,
    required CountryResponse response,
  }) {
    existingModel.jsonData = response.toRawJson();
    existingModel.lastUpdated = DateTime.now();
  }
}