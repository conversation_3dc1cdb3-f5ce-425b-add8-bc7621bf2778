import '../models/state_response.dart';
import '../models/state_model.dart' as state_realm;

class StateMapper {
  static state_realm.StateModel toModel(StateResponse response) {
    return state_realm.StateModel(
      "state",
      response.toRawJson(),
      DateTime.now(),
    );
  }

  static StateResponse toEntity(state_realm.StateModel model) {
    return StateResponse.fromRawJson(model.jsonData);
  }

  static void updateModel({
    required state_realm.StateModel existingModel,
    required StateResponse response,
  }) {
    existingModel.jsonData = response.toRawJson();
    existingModel.lastUpdated = DateTime.now();
  }
}