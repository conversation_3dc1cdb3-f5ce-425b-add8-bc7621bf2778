import 'package:flutter/material.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_response.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../shared/widgets/html_text_widget.dart';

enum NotificationType {
  urgent,
  money,
  message,
}

// Shared helper methods
class NotificationCardHelpers {
  static String getIconAsset(NotificationType type) {
    switch (type) {
      case NotificationType.urgent:
        return AppAssets.alertLightening;
      case NotificationType.money:
        return AppAssets.notificationDollar;
      case NotificationType.message:
        return AppAssets.alertMessage;
    }
  }

  static Color getIconColor(NotificationType type) {
    switch (type) {
      case NotificationType.urgent:
        return AppColors.richOrange;
      case NotificationType.money:
        return AppColors.darkYellow15;
      case NotificationType.message:
        return AppColors.black;
    }
  }

  static Color getBackgroundColor(NotificationType type) {
    switch (type) {
      case NotificationType.urgent:
        return AppColors.richOrange15;
      case NotificationType.money:
        return AppColors.richYellow15;
      case NotificationType.message:
        return AppColors.lightGrey2;
    }
  }

  static Color getTextColor(NotificationType type) {
    switch (type) {
      case NotificationType.urgent:
        return AppColors.darkOrange50;
      case NotificationType.money:
        return AppColors.darkYellow50;
      case NotificationType.message:
        return AppColors.black;
    }
  }
}

// Task Details version - optimized for alert_view.dart
class TaskDetailsNotificationCard extends StatelessWidget {
  final NotificationType type;
  final String message;
  final String client;
  final String subject;
  final String location;
  final String timeAgo;
  final String budget;
  final VoidCallback? onTap;

  const TaskDetailsNotificationCard({
    super.key,
    required this.type,
    required this.message,
    required this.client,
    required this.subject,
    required this.location,
    required this.timeAgo,
    required this.budget,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: NotificationCardHelpers.getBackgroundColor(
                                type),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Image.asset(
                              NotificationCardHelpers.getIconAsset(type),
                              color: NotificationCardHelpers.getIconColor(type),
                              width: 20,
                              height: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          color:
                              NotificationCardHelpers.getBackgroundColor(type),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              HtmlTextWidget(
                                text: message,
                                style:
                                    textTheme.montserratParagraphSmall.copyWith(
                                  color: NotificationCardHelpers.getTextColor(
                                      type),
                                ),
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  border: Border.all(
                                    color: AppColors.black20,
                                    width: 1,
                                  ),
                                ),
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          client,
                                          style: textTheme
                                              .montserratTitleExtraSmall
                                              .copyWith(
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        const Spacer(),
                                        const Icon(
                                          Icons.access_time,
                                          size: 18,
                                          color: AppColors.blackTint1,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          budget,
                                          style: textTheme
                                              .montserratTitleExtraSmall
                                              .copyWith(
                                            color: AppColors.blackTint1,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      subject,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: textTheme.montserratTableSmall,
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Image.asset(
                                          AppAssets.notificationLocation,
                                          scale: 5,
                                          color: Colors.black,
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            location,
                                            style: textTheme
                                                .montserratTableSmall
                                                .copyWith(
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  timeAgo,
                                  style: textTheme.montserratRegular.copyWith(
                                    fontSize: 12,
                                    color: AppColors.blackTint1,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Notification Page version - optimized for notification_page.dart
class AlertsNotificationCard extends StatelessWidget {
  final NotificationType type;
  final String comment;
  final String clientName;
  final String title;
  final String storeAddress;
  final String timeAgo;
  final String taskDuration;
  final VoidCallback? onTap;
  final Alert alert;

  const AlertsNotificationCard({
    super.key,
    required this.type,
    required this.comment,
    required this.clientName,
    required this.title,
    required this.storeAddress,
    required this.timeAgo,
    required this.taskDuration,
    this.onTap,
    required this.alert,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: NotificationCardHelpers.getBackgroundColor(
                                type),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Image.asset(
                              NotificationCardHelpers.getIconAsset(type),
                              color: NotificationCardHelpers.getIconColor(type),
                              width: 20,
                              height: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          color:
                              NotificationCardHelpers.getBackgroundColor(type),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              HtmlTextWidget(
                                text: comment,
                                style:
                                    textTheme.montserratParagraphSmall.copyWith(
                                  color: NotificationCardHelpers.getTextColor(
                                      type),
                                ),
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  border: Border.all(
                                    color: AppColors.black20,
                                    width: 1,
                                  ),
                                ),
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Flexible(
                                          child: HtmlTextWidget(
                                            text: alert.shortDescription,
                                            style: textTheme
                                                .montserratTitleExtraSmall
                                                .copyWith(
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        const Spacer(),
                                        const Icon(
                                          Icons.access_time,
                                          size: 18,
                                          color: AppColors.blackTint1,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          taskDuration,
                                          style: textTheme
                                              .montserratTitleExtraSmall
                                              .copyWith(
                                            color: AppColors.blackTint1,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      title,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: textTheme.montserratTableSmall,
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Image.asset(
                                          AppAssets.notificationLocation,
                                          scale: 5,
                                          color: Colors.black,
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            storeAddress,
                                            style: textTheme
                                                .montserratTableSmall
                                                .copyWith(
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  timeAgo,
                                  style: textTheme.montserratRegular.copyWith(
                                    fontSize: 12,
                                    color: AppColors.blackTint1,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
