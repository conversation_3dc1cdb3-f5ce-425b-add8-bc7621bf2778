import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/checkin_response_entity.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {}

class DashboardLoading extends DashboardState {}

class DashboardLoaded extends DashboardState {
  final TasksResponseEntity response;
  final int countUnscheduled;
  final int countScheduled;
  final int countPos;
  final int countCompleted;
  final int countToday;
  final int countCompletedToday;
  final OpenTaskResponseEntity? openTaskResponse;
  final int countTaken;
  final int countAvailable;
  final int countAutoschedule;
  final bool otShow;

  const DashboardLoaded({
    required this.response,
    required this.countUnscheduled,
    required this.countScheduled,
    required this.countPos,
    required this.countCompleted,
    required this.countToday,
    required this.countCompletedToday,
    this.openTaskResponse,
    this.countTaken = 0,
    this.countAvailable = 0,
    this.countAutoschedule = 0,
    this.otShow = false,
  });

  @override
  List<Object?> get props => [
        response,
        countUnscheduled,
        countScheduled,
        countPos,
        countCompleted,
        countToday,
        countCompletedToday,
        openTaskResponse,
        countTaken,
        countAvailable,
        countAutoschedule,
        otShow,
      ];
}

class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}

class CheckinLoading extends DashboardState {}

class CheckinSuccess extends DashboardState {
  final CheckinResponseEntity response;
  final bool isDayEnded;
  final String? elapsedTime;

  const CheckinSuccess({
    required this.response,
    required this.isDayEnded,
    this.elapsedTime,
  });

  @override
  List<Object?> get props => [response, isDayEnded, elapsedTime];
}

class CheckinError extends DashboardState {
  final String message;

  const CheckinError(this.message);

  @override
  List<Object?> get props => [message];
}
