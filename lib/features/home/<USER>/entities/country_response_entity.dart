class CountryResponseEntity {
    final Data data;

    CountryResponseEntity({
        required this.data,
    });

    factory CountryResponseEntity.fromJson(Map<String, dynamic> json) => CountryResponseEntity(
        data: Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "data": data.toJson(),
    };

}

class Data {
    final List<Country> countries;

    Data({
        required this.countries,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        countries: List<Country>.from(json["countries"].map((x) => Country.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "countries": List<dynamic>.from(countries.map((x) => x.toJson())),
    };

}

class Country {
    final String country;
    final int countryId;

    Country({
        required this.country,
        required this.countryId,
    });

    factory Country.fromJson(Map<String, dynamic> json) => Country(
        country: json["country"],
        countryId: json["country_id"],
    );

    Map<String, dynamic> toJson() => {
        "country": country,
        "country_id": countryId,
    };

}
