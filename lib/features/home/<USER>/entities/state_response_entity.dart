class StateResponseEntity {
    final Data data;

    StateResponseEntity({
        required this.data,
    });

    factory StateResponseEntity.fromJson(Map<String, dynamic> json) => StateResponseEntity(
        data: Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "data": data.toJson(),
    };

}

class Data {
    final List<State> states;

    Data({
        required this.states,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        states: List<State>.from(json["states"].map((x) => State.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "states": List<dynamic>.from(states.map((x) => x.toJson())),
    };

}

class State {
    final String state;
    final int stateId;
    final String country;
    final int countryId;

    State({
        required this.state,
        required this.stateId,
        required this.country,
        required this.countryId,
    });

    factory State.fromJson(Map<String, dynamic> json) => State(
        state: json["state"],
        stateId: json["state_id"],
        country: json["country"],
        countryId: json["country_id"],
    );

    Map<String, dynamic> toJson() => {
        "state": state,
        "state_id": stateId,
        "country": country,
        "country_id": countryId,
    };

}
