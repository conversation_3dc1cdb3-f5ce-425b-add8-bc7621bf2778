import 'dart:convert';
import '../../domain/entities/state_response_entity.dart';

class StateResponse {
  final StateData? data;

  StateResponse({
    this.data,
  });

  factory StateResponse.fromRawJson(String str) =>
      StateResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StateResponse.fromJson(Map<String, dynamic> json) => StateResponse(
        data: json["data"] == null ? null : StateData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };

  StateResponseEntity toEntity() {
    return StateResponseEntity(
      data: data?.toEntity() ?? Data(states: []),
    );
  }
}

class StateData {
  final List<StateModel> states;

  StateData({
    required this.states,
  });

  factory StateData.fromJson(Map<String, dynamic> json) => StateData(
        states: json["states"] == null
            ? []
            : List<StateModel>.from(json["states"].map((x) => StateModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "states": List<dynamic>.from(states.map((x) => x.toJson())),
      };

  Data toEntity() {
    return Data(
      states: states.map((state) => state.toEntity()).toList(),
    );
  }
}

class StateModel {
  final String state;
  final int stateId;
  final String country;
  final int countryId;

  StateModel({
    required this.state,
    required this.stateId,
    required this.country,
    required this.countryId,
  });

  factory StateModel.fromJson(Map<String, dynamic> json) => StateModel(
        state: json["state"],
        stateId: json["state_id"],
        country: json["country"],
        countryId: json["country_id"],
      );

  Map<String, dynamic> toJson() => {
        "state": state,
        "state_id": stateId,
        "country": country,
        "country_id": countryId,
      };

  State toEntity() {
    return State(
      state: state,
      stateId: stateId,
      country: country,
      countryId: countryId,
    );
  }
}