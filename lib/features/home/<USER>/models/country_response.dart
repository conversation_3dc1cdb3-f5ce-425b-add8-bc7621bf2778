import 'dart:convert';
import '../../domain/entities/country_response_entity.dart';

class CountryResponse {
  final CountryData? data;

  CountryResponse({
    this.data,
  });

  factory CountryResponse.fromRawJson(String str) =>
      CountryResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CountryResponse.fromJson(Map<String, dynamic> json) => CountryResponse(
        data: json["data"] == null ? null : CountryData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };

  CountryResponseEntity toEntity() {
    return CountryResponseEntity(
      data: data?.toEntity() ?? Data(countries: []),
    );
  }
}

class CountryData {
  final List<CountryModel> countries;

  CountryData({
    required this.countries,
  });

  factory CountryData.fromJson(Map<String, dynamic> json) => CountryData(
        countries: json["countries"] == null
            ? []
            : List<CountryModel>.from(json["countries"].map((x) => CountryModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "countries": List<dynamic>.from(countries.map((x) => x.toJson())),
      };

  Data toEntity() {
    return Data(
      countries: countries.map((country) => country.toEntity()).toList(),
    );
  }
}

class CountryModel {
  final String country;
  final int countryId;

  CountryModel({
    required this.country,
    required this.countryId,
  });

  factory CountryModel.fromJson(Map<String, dynamic> json) => CountryModel(
        country: json["country"],
        countryId: json["country_id"],
      );

  Map<String, dynamic> toJson() => {
        "country": country,
        "country_id": countryId,
      };

  Country toEntity() {
    return Country(
      country: country,
      countryId: countryId,
    );
  }
}