import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Result class for POS status calculations
class PosStatusResult {
  final int posReceivedCount;
  final int totalPosItems;
  final bool isCompleted;
  final bool hasAnyReceived;

  PosStatusResult({
    required this.posReceivedCount,
    required this.totalPosItems,
    required this.isCompleted,
    required this.hasAnyReceived,
  });

  /// Creates a PosStatusResult with zero values (for error cases or no data)
  factory PosStatusResult.empty() {
    return PosStatusResult(
      posReceivedCount: 0,
      totalPosItems: 0,
      isCompleted: false,
      hasAnyReceived: false,
    );
  }
}

/// Utility class for POS status calculations
/// Eliminates code duplication between pos_page.dart and pos_received_view.dart
class PosStatusUtils {
  // Private constructor to prevent instantiation
  PosStatusUtils._();

  /// Calculates POS status for a given task by checking the database
  /// Returns PosStatusResult with counts and status flags
  static PosStatusResult calculatePosStatus(TaskDetail task) {
    logger(
        '[POS_CALC] Starting calculatePosStatus for task: ${task.taskId}, store: ${task.storeName}');
    logger('[POS_CALC] Task posReceived field: ${task.posReceived}');
    logger('[POS_CALC] Task posItems count: ${task.posItems?.length ?? 0}');

    try {
      final realm = RealmDatabase.instance.realm;

      // Get POS response items from database for this task
      logger(
          '[POS_CALC] Searching for taskId: ${task.taskId} (type: ${task.taskId.runtimeType})');

      final posResponseItems = realm.query<PosResponseItemModel>(
        'taskId == ${task.taskId}',
      );

      logger(
          '[POS_CALC] Database query results: ${posResponseItems.length} POS response items found');

      // Debug: Show what task IDs are actually in the database
      final allPosItems = realm.query<PosResponseItemModel>('TRUEPREDICATE');
      logger('[POS_CALC] Total POS items in database: ${allPosItems.length}');
      if (allPosItems.isNotEmpty) {
        final uniqueTaskIds = allPosItems.map((e) => e.taskId).toSet().toList();
        logger('[POS_CALC] Available taskIds in database: $uniqueTaskIds');
        logger(
            '[POS_CALC] First few items: ${allPosItems.take(3).map((e) => 'taskId=${e.taskId}(${e.taskId.runtimeType}), received=${e.received}').toList()}');
      }

      if (posResponseItems.isNotEmpty) {
        for (int i = 0; i < posResponseItems.length; i++) {
          final item = posResponseItems[i];
          logger(
              '[POS_CALC] Item $i: taskId=${item.taskId}, received=${item.received}, storeName=${item.storeName}');
        }
      }

      // Check cached data first, then fall back to task.posReceived
      bool hasAnyReceived = false;
      if (posResponseItems.isNotEmpty) {
        // Use cached data if available (case-insensitive comparison)
        logger(
            '[POS_CALC] Using cached database data for hasAnyReceived calculation');
        hasAnyReceived = posResponseItems
            .any((item) => item.received?.toLowerCase() == "true");
        logger('[POS_CALC] Cached data hasAnyReceived result: $hasAnyReceived');
      } else {
        // Fall back to original task.posReceived field
        logger(
            '[POS_CALC] No cached data found, falling back to task.posReceived field');
        final posReceivedStr = task.posReceived?.toString().toLowerCase();
        logger(
            '[POS_CALC] Original posReceived string (lowercase): $posReceivedStr');
        hasAnyReceived = (posReceivedStr == 'true' || posReceivedStr == '1');
        logger('[POS_CALC] Fallback hasAnyReceived result: $hasAnyReceived');
      }

      final totalPosItems = task.posItems?.length ?? 0;
      final posReceivedCount = hasAnyReceived ? totalPosItems : 0;
      final isCompleted =
          totalPosItems == posReceivedCount && totalPosItems > 0;

      logger('[POS_CALC] Final calculations:');
      logger('[POS_CALC] - totalPosItems: $totalPosItems');
      logger('[POS_CALC] - hasAnyReceived: $hasAnyReceived');
      logger('[POS_CALC] - posReceivedCount: $posReceivedCount');
      logger('[POS_CALC] - isCompleted: $isCompleted');

      return PosStatusResult(
        posReceivedCount: posReceivedCount,
        totalPosItems: totalPosItems,
        isCompleted: isCompleted,
        hasAnyReceived: hasAnyReceived,
      );
    } catch (e) {
      // Fallback to original logic if database read fails
      logger('[POS_CALC] ❌ Database query failed with error: $e');
      logger('[POS_CALC] Falling back to original task.posReceived logic');
      return _calculatePosStatusFallback(task);
    }
  }

  /// Fallback method using the original task.posReceived field
  /// Used when database query fails
  static PosStatusResult _calculatePosStatusFallback(TaskDetail task) {
    logger('[POS_FALLBACK] Using fallback method for task: ${task.taskId}');
    logger('[POS_FALLBACK] Original task.posReceived: ${task.posReceived}');

    final totalPosItems = task.posItems?.length ?? 0;
    final posReceivedStr = task.posReceived?.toString().toLowerCase();

    logger('[POS_FALLBACK] totalPosItems: $totalPosItems');
    logger('[POS_FALLBACK] posReceivedStr (lowercase): $posReceivedStr');

    bool hasAnyReceived = false;
    if (posReceivedStr == 'true' || posReceivedStr == '1') {
      hasAnyReceived = true;
    }

    logger('[POS_FALLBACK] hasAnyReceived result: $hasAnyReceived');

    final posReceivedCount = hasAnyReceived ? totalPosItems : 0;
    final isCompleted = totalPosItems == posReceivedCount && totalPosItems > 0;

    logger('[POS_FALLBACK] Final fallback calculations:');
    logger('[POS_FALLBACK] - posReceivedCount: $posReceivedCount');
    logger('[POS_FALLBACK] - isCompleted: $isCompleted');

    return PosStatusResult(
      posReceivedCount: posReceivedCount,
      totalPosItems: totalPosItems,
      isCompleted: isCompleted,
      hasAnyReceived: hasAnyReceived,
    );
  }

  /// Formats the POS status for display
  /// Returns a string like "2 of 5 received"
  static String formatPosStatus(PosStatusResult result) {
    return '${result.posReceivedCount} of ${result.totalPosItems} received';
  }

  /// Formats the POS status for display with custom text
  /// Returns a string like "2 of 5 delivered"
  static String formatPosStatusWithText(
      PosStatusResult result, String actionText) {
    return '${result.posReceivedCount} of ${result.totalPosItems} $actionText';
  }
}
